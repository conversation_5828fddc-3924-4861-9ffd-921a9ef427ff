#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome下载完整性验证器
用于验证下载的Chrome Portable文件是否完整和有效

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-26
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import requests
import re

class ChromeDownloadValidator:
    """Chrome下载文件完整性验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = logging.getLogger("ChromeValidator")
        
        # 文件验证标准
        self.validation_criteria = {
            "min_file_size": 50 * 1024 * 1024,  # 最小50MB
            "max_file_size": 200 * 1024 * 1024,  # 最大200MB
            "valid_extensions": [".exe", ".paf.exe"],
            "pe_header": b"MZ",  # PE文件头
            "expected_patterns": [
                b"GoogleChromePortable",
                b"PortableApps",
                b"Chrome"
            ]
        }
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """验证下载文件的完整性
        
        Args:
            file_path: 文件路径
            
        Returns:
            验证结果字典
        """
        result = {
            "is_valid": False,
            "file_exists": False,
            "file_size": 0,
            "file_size_mb": 0.0,
            "has_valid_header": False,
            "has_expected_content": False,
            "validation_errors": [],
            "validation_warnings": []
        }
        
        try:
            # 检查文件是否存在
            if not file_path.exists():
                result["validation_errors"].append("文件不存在")
                return result
            
            result["file_exists"] = True
            
            # 检查文件大小
            file_size = file_path.stat().st_size
            result["file_size"] = file_size
            result["file_size_mb"] = file_size / (1024 * 1024)
            
            if file_size < self.validation_criteria["min_file_size"]:
                result["validation_errors"].append(
                    f"文件大小过小: {result['file_size_mb']:.1f}MB < {self.validation_criteria['min_file_size']/(1024*1024):.1f}MB"
                )
            elif file_size > self.validation_criteria["max_file_size"]:
                result["validation_warnings"].append(
                    f"文件大小异常大: {result['file_size_mb']:.1f}MB > {self.validation_criteria['max_file_size']/(1024*1024):.1f}MB"
                )
            
            # 检查文件扩展名
            if not any(str(file_path).lower().endswith(ext) for ext in self.validation_criteria["valid_extensions"]):
                result["validation_warnings"].append(f"文件扩展名可能不正确: {file_path.suffix}")
            
            # 检查文件头
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(2)
                    if header == self.validation_criteria["pe_header"]:
                        result["has_valid_header"] = True
                    else:
                        result["validation_errors"].append(f"文件头无效: {header.hex()} (期望: {self.validation_criteria['pe_header'].hex()})")
            except Exception as e:
                result["validation_errors"].append(f"无法读取文件头: {e}")
            
            # 检查文件内容
            try:
                with open(file_path, 'rb') as f:
                    # 读取前1MB内容进行检查
                    content = f.read(1024 * 1024)
                    
                    found_patterns = 0
                    for pattern in self.validation_criteria["expected_patterns"]:
                        if pattern in content:
                            found_patterns += 1
                    
                    if found_patterns >= 2:  # 至少找到2个期望的模式
                        result["has_expected_content"] = True
                    else:
                        result["validation_errors"].append(f"文件内容验证失败，只找到 {found_patterns} 个期望模式")
                        
            except Exception as e:
                result["validation_errors"].append(f"无法读取文件内容: {e}")
            
            # 综合判断
            result["is_valid"] = (
                result["file_exists"] and
                result["file_size"] >= self.validation_criteria["min_file_size"] and
                result["has_valid_header"] and
                result["has_expected_content"] and
                len(result["validation_errors"]) == 0
            )
            
            return result
            
        except Exception as e:
            result["validation_errors"].append(f"验证过程异常: {e}")
            return result
    
    def validate_download_url(self, url: str) -> Dict[str, Any]:
        """验证下载链接是否有效
        
        Args:
            url: 下载链接
            
        Returns:
            验证结果字典
        """
        result = {
            "is_valid": False,
            "status_code": 0,
            "content_length": 0,
            "content_type": "",
            "is_direct_download": False,
            "errors": []
        }
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.head(url, headers=headers, timeout=15, allow_redirects=True)
            result["status_code"] = response.status_code
            
            if response.status_code == 200:
                result["content_length"] = int(response.headers.get('content-length', 0))
                result["content_type"] = response.headers.get('content-type', '')
                
                # 检查是否是直接下载链接
                if 'application/octet-stream' in result["content_type"] or \
                   'application/x-msdownload' in result["content_type"] or \
                   result["content_length"] > 10 * 1024 * 1024:  # 大于10MB
                    result["is_direct_download"] = True
                
                result["is_valid"] = result["is_direct_download"]
            else:
                result["errors"].append(f"HTTP状态码错误: {response.status_code}")
                
        except Exception as e:
            result["errors"].append(f"链接验证失败: {e}")
        
        return result
    
    def get_real_download_url(self, page_url: str) -> Optional[str]:
        """从下载页面提取真实的下载链接
        
        Args:
            page_url: 下载页面URL
            
        Returns:
            真实的下载链接，如果提取失败返回None
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(page_url, headers=headers, timeout=30)
            if response.status_code != 200:
                return None
            
            content = response.text
            
            # 查找下载链接的模式
            patterns = [
                r'href="([^"]*GoogleChromePortable[^"]*\.paf\.exe[^"]*)"',
                r'href="([^"]*\.paf\.exe[^"]*)"',
                r'window\.location\s*=\s*["\']([^"\']*\.paf\.exe[^"\']*)["\']',
                r'download\s*=\s*["\']([^"\']*\.paf\.exe[^"\']*)["\']'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    # 构建完整URL
                    if match.startswith('http'):
                        download_url = match
                    elif match.startswith('/'):
                        from urllib.parse import urljoin
                        download_url = urljoin(page_url, match)
                    else:
                        continue
                    
                    # 验证链接
                    validation = self.validate_download_url(download_url)
                    if validation["is_valid"]:
                        return download_url
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取下载链接失败: {e}")
            return None

# 创建全局验证器实例
chrome_validator = ChromeDownloadValidator()

def validate_chrome_file(file_path: Path) -> Dict[str, Any]:
    """验证Chrome文件的便捷函数"""
    return chrome_validator.validate_file(file_path)

def validate_chrome_download_url(url: str) -> Dict[str, Any]:
    """验证Chrome下载链接的便捷函数"""
    return chrome_validator.validate_download_url(url)

def get_real_chrome_download_url(page_url: str) -> Optional[str]:
    """获取真实Chrome下载链接的便捷函数"""
    return chrome_validator.get_real_download_url(page_url)

if __name__ == "__main__":
    print("🔍 Chrome下载完整性验证器测试")
    print("=" * 50)
    
    # 测试已下载的文件
    download_dir = Path("下载")
    if download_dir.exists():
        for file_path in download_dir.glob("*.paf.exe"):
            print(f"\n📁 验证文件: {file_path.name}")
            result = validate_chrome_file(file_path)
            
            print(f"文件大小: {result['file_size_mb']:.1f}MB")
            print(f"文件头有效: {'✅' if result['has_valid_header'] else '❌'}")
            print(f"内容有效: {'✅' if result['has_expected_content'] else '❌'}")
            print(f"整体有效: {'✅' if result['is_valid'] else '❌'}")
            
            if result['validation_errors']:
                print("❌ 错误:")
                for error in result['validation_errors']:
                    print(f"   - {error}")
            
            if result['validation_warnings']:
                print("⚠️ 警告:")
                for warning in result['validation_warnings']:
                    print(f"   - {warning}")
    
    print("\n✅ 验证器测试完成")
